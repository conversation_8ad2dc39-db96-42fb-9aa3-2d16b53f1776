const BITS_IN_BYTE: u8 = 8;

#[derive(Debug)]
pub struct BitStreamWriter {
    buffer: Vec<u8>,
    /// Next position of 0 means the last byte is full or we are on the first byte. Next data will be written on a new byte.
    next_bit_position: u8,
}

impl BitStreamWriter {
    pub fn new() -> Self {
        BitStreamWriter {
            buffer: Vec::new(),
            next_bit_position: 0,
        }
    }

    pub fn write_n_bit_data(&mut self, data: u8, num_bits: u8) {
        tracing::debug!("Writing {} bits of data: {:08b}", num_bits, data);
        tracing::debug!("Current position: {:?}", self.next_bit_position);
        tracing::debug!("Before: {:02X?}", self.buffer);
        if num_bits > BITS_IN_BYTE {
            tracing::error!("Can't write more than a byte with write_n_bit_data");
            return;
        }
        if self.next_bit_position + num_bits <= BITS_IN_BYTE {
            let mut next_byte = self.buffer.pop().unwrap_or(0u8);
            let left_aligned =
                (data & ((1 << num_bits) - 1)) << ((8 - self.next_bit_position) - num_bits);
            next_byte |= left_aligned;
            self.buffer.push(next_byte);
        }
        self.next_bit_position += num_bits;
        if self.next_bit_position >= BITS_IN_BYTE {
            self.next_bit_position = 0;
        }

        tracing::debug!("buffer: {:02X?}", self.buffer);
        tracing::debug!("Next bit position: {:?}", self.next_bit_position);
    }

    pub fn into_bytes(&self) -> Vec<u8> {
        self.buffer.clone()
    }

}
