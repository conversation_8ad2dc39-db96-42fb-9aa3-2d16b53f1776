use crate::exifier::grammar::EXIGrammar;
use crate::exifier::prelude::*;
use crate::exifier::writer::BitStreamWriter;
use quick_xml::events::Event;
use quick_xml::reader::Reader;
use serde_json::Value;
use tracing::instrument;
use xml2json_rs::{
    Declaration, Encoding, Indentation, JsonBuilder, JsonConfig, Version, XmlBuilder, XmlConfig,
};

use std::error::Error;

#[derive(Debug)]
pub struct Exifier {
    grammar: EXIGrammar,
    xml: String,
    bitstream_writer: BitStreamWriter,
}

impl Exifier {
    pub fn default() -> Self {
        Exifier {
            grammar: EXIGrammar::default(),
            xml: String::new(),
            bitstream_writer: BitStreamWriter::new(),
        }
    }

    pub fn load_grammar(&mut self, exi_grammar: EXIGrammar) -> Result<()> {
        tracing::debug!(
            "Loading EXI grammar with {} grammar rules",
            exi_grammar.grs.grammar.len()
        );
        tracing::debug!(
            "Document grammar ID: {}",
            exi_grammar.grs.document_grammar_id
        );
        tracing::debug!(
            "Fragment grammar ID: {}",
            exi_grammar.grs.fragment_grammar_id
        );
        // TODO: Store the grammar for use in encoding/decoding
        self.grammar = exi_grammar;
        Ok(())
    }

    pub fn encode(&mut self, json_value: Value) -> Result<Vec<u8>> {
        tracing::debug!("Encode JSON value: {}", json_value);

        // Convert JSON value to string for XML conversion
        let json_string = serde_json::to_string(&json_value).unwrap();
        self.prepare_encoder(&json_string);
        self.encode_header();
        self.encode_body();

        tracing::debug!("Result so far: {:02X?}", self.bitstream_writer.into_bytes());
        // Return the incoming JSON as a vector of bytes
        Ok(json_string.into_bytes())
    }

    pub fn decode(&self, encoded_bytes: Vec<u8>) -> Result<Value> {
        // Decode the incoming bytes to a string
        let json_string = String::from_utf8(encoded_bytes).unwrap();
        tracing::debug!("Decoded bytes to JSON string: {}", json_string);

        // Parse the JSON string into a Value
        let json_value: Value = serde_json::from_str(&json_string)
            .map_err(|e| crate::exifier::Error::Generic(format!("Failed to parse JSON: {}", e)))?;

        tracing::debug!("Parsed JSON value: {}", json_value);
        Ok(json_value)
    }

    fn parse_xml(&self, xml: &str) {
        let mut reader = Reader::from_str(xml);
        let mut buf = Vec::new();
        loop {
            match reader.read_event_into(&mut buf) {
                Ok(Event::Start(e)) => tracing::debug!("Start element: {:?}", e.name()),
                Ok(Event::Text(e)) => tracing::debug!("Text: {:?}", e.unescape().unwrap()),
                Ok(Event::End(e)) => tracing::debug!("End element: {:?}", e.name()),
                Ok(Event::Eof) => break,
                Err(e) => panic!("Error: {:?}", e),
                _ => (),
            }
            buf.clear();
        }
    }

    fn prepare_encoder(&mut self, json_string: &str) {
        let mut xml_builder = XmlConfig::new().root_name(String::from("map")).finalize();
        self.xml = xml_builder.build_from_json_string(&json_string).unwrap();
        tracing::debug!("Generated XML: {}", &self.xml);
    }

    fn encode_header(&mut self) -> Result<()> {
        self.bitstream_writer.write_n_bit_data(2, 2);
        // Options bit 0 (1 bit)
        self.bitstream_writer.write_n_bit_data(0, 1);
        // Header version 00000 (5 bits)
        self.bitstream_writer.write_n_bit_data(0, 5);
        Ok(())
    }

    fn encode_body(&mut self) -> Result<()> {
        //self.parse_xml(&self.xml);
        Ok(())
    }
}
